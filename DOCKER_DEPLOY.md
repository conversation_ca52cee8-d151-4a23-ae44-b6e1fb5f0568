# 华夏中医网站 Docker 部署指南

## 🚀 快速开始

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

### 一键部署

1. **克隆项目**
```bash
git clone <repository-url>
cd hxzy
```

2. **运行部署脚本**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

3. **选择部署模式**
   - 选择 `1` 进行生产环境部署
   - 选择 `2` 进行开发环境部署

4. **访问应用**
   - 生产环境: http://localhost:3000
   - 开发环境: http://localhost:3001

## 📋 详细部署步骤

### 生产环境部署

1. **配置环境变量**
```bash
cp .env.docker .env
# 编辑 .env 文件，填入实际的配置值
```

2. **启动服务**
```bash
docker-compose up -d
```

3. **运行数据库迁移**
```bash
docker-compose exec app npx prisma migrate deploy
```

### 开发环境部署

1. **启动开发环境**
```bash
docker-compose -f docker-compose.dev.yml up -d
```

2. **运行数据库迁移**
```bash
docker-compose -f docker-compose.dev.yml exec app-dev npx prisma migrate dev
```

## 🔧 配置说明

### 环境变量配置

在 `.env` 文件中配置以下变量：

```bash
# 第三方登录 (可选)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# 微信登录 (可选)
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# Cloudflare R2 存储 (可选)
R2_ACCOUNT_ID=your-r2-account-id
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
```

### 服务架构

- **app**: Next.js 应用服务器
- **postgres**: PostgreSQL 数据库
- **redis**: Redis 缓存
- **nginx**: 反向代理 (可选)

## 📊 服务管理

### 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
docker-compose logs -f app

# 重启服务
docker-compose restart
docker-compose restart app

# 停止服务
docker-compose down

# 完全清理 (包括数据)
docker-compose down -v
```

### 数据库管理

```bash
# 进入数据库容器
docker-compose exec postgres psql -U hxzy_user -d hxzy

# 备份数据库
docker-compose exec postgres pg_dump -U hxzy_user hxzy > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U hxzy_user hxzy < backup.sql
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   - 修改 docker-compose.yml 中的端口映射
   - 确保 3000, 5432, 6379 端口未被占用

2. **数据库连接失败**
   - 检查数据库是否正常启动: `docker-compose logs postgres`
   - 确认环境变量配置正确

3. **应用启动失败**
   - 查看应用日志: `docker-compose logs app`
   - 检查 Prisma 迁移是否成功执行

### 健康检查

访问 http://localhost:3000/api/health 检查服务状态

## 🚀 生产环境优化

### SSL 证书配置

1. 将 SSL 证书放在 `nginx/ssl/` 目录
2. 取消注释 `nginx/nginx.conf` 中的 HTTPS 配置
3. 更新域名配置

### 性能优化

1. **数据库优化**
   - 调整 PostgreSQL 配置
   - 设置连接池
   - 配置备份策略

2. **缓存优化**
   - 配置 Redis 持久化
   - 设置缓存策略
   - 启用 Nginx 缓存

## 📈 监控和日志

### 日志管理

```bash
# 实时查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f app
docker-compose logs -f postgres
docker-compose logs -f redis
```

### 监控指标

- 应用健康状态: `/api/health`
- 数据库连接状态
- Redis 缓存状态
- 磁盘使用情况

## 🔄 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建并部署
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 运行数据库迁移
docker-compose exec app npx prisma migrate deploy
```

## 📞 技术支持

如遇到部署问题，请检查：
1. Docker 和 Docker Compose 版本
2. 系统资源使用情况
3. 网络连接状态
4. 日志错误信息
