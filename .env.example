# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/hxzy"

# NextAuth 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# 第三方登录配置
# GitHub OAuth
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# 微信登录配置
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_APP_SECRET="your-wechat-app-secret"
WECHAT_TREDIRECT_URL="http://localhost:3000/api/wechat/callback"

# 图片存储配置
# 存储类型: 'local' | 'r2'
STORAGE_TYPE=local

# 本地存储配置
LOCAL_UPLOAD_DIR=public/uploads
LOCAL_BASE_URL=http://localhost:3000

# Cloudflare R2 配置
R2_ACCOUNT_ID=your-account-id
R2_ACCESS_KEY_ID=your-access-key-id
R2_SECRET_ACCESS_KEY=your-secret-access-key
R2_BUCKET_NAME=hxzy
R2_ENDPOINT=https://ae32780581ce1b7c7495b91edf6ad810.r2.cloudflarestorage.com
R2_PUBLIC_URL=https://your-custom-domain.com
# 或者使用 R2 的默认 URL: https://pub-xxxxx.r2.dev

# 图片处理配置
MAX_FILE_SIZE=5242880  # 5MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/gif
MAX_FILES_PER_UPLOAD=9



# 其他配置...
