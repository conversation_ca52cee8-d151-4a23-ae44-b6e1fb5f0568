# 华夏中医网站 Docker 部署 Makefile

.PHONY: help build up down logs restart clean dev dev-down prod-build prod-up migrate health

# 默认目标
help:
	@echo "华夏中医网站 Docker 部署命令："
	@echo ""
	@echo "开发环境："
	@echo "  make dev          - 启动开发环境"
	@echo "  make dev-down     - 停止开发环境"
	@echo "  make dev-logs     - 查看开发环境日志"
	@echo ""
	@echo "生产环境："
	@echo "  make prod-build   - 构建生产环境镜像"
	@echo "  make prod-up      - 启动生产环境"
	@echo "  make prod-down    - 停止生产环境"
	@echo "  make prod-logs    - 查看生产环境日志"
	@echo ""
	@echo "数据库："
	@echo "  make migrate      - 运行数据库迁移"
	@echo "  make db-reset     - 重置数据库"
	@echo ""
	@echo "维护："
	@echo "  make health       - 检查服务健康状态"
	@echo "  make clean        - 清理所有容器和数据"
	@echo "  make restart      - 重启所有服务"

# 开发环境
dev:
	@echo "🛠️ 启动开发环境..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "⏳ 等待服务启动..."
	sleep 10
	@echo "🗄️ 运行数据库迁移..."
	docker-compose -f docker-compose.dev.yml exec app-dev npx prisma migrate dev || true
	@echo "✅ 开发环境启动完成！访问: http://localhost:3001"

dev-down:
	@echo "🛑 停止开发环境..."
	docker-compose -f docker-compose.dev.yml down

dev-logs:
	docker-compose -f docker-compose.dev.yml logs -f

# 生产环境
prod-build:
	@echo "🏗️ 构建生产环境镜像..."
	docker-compose build --no-cache

prod-up:
	@echo "🚀 启动生产环境..."
	docker-compose up -d
	@echo "⏳ 等待服务启动..."
	sleep 15
	@echo "🗄️ 运行数据库迁移..."
	docker-compose exec app npx prisma migrate deploy || true
	@echo "✅ 生产环境启动完成！访问: http://localhost:3000"

prod-down:
	@echo "🛑 停止生产环境..."
	docker-compose down

prod-logs:
	docker-compose logs -f

# 数据库操作
migrate:
	@echo "🗄️ 运行数据库迁移..."
	docker-compose exec app npx prisma migrate deploy

db-reset:
	@echo "⚠️ 重置数据库..."
	docker-compose exec app npx prisma migrate reset --force

# 维护操作
health:
	@echo "🔍 检查服务健康状态..."
	@curl -f http://localhost:3000/api/health || echo "❌ 服务不健康"

restart:
	@echo "🔄 重启所有服务..."
	docker-compose restart

clean:
	@echo "🧹 清理所有容器和数据..."
	docker-compose down -v
	docker-compose -f docker-compose.dev.yml down -v
	docker system prune -f

# 快速部署
deploy: prod-build prod-up
	@echo "🎉 部署完成！"

# 快速开发
start-dev: dev
	@echo "🎉 开发环境就绪！"
