import { prisma } from './prisma';
import { cacheManager } from './cache';

// 数据库查询优化工具
export class DatabaseOptimizer {
  // 批量查询优化
  static async batchQuery<T>(
    queries: (() => Promise<T>)[],
    batchSize: number = 10
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(query => query()));
      results.push(...batchResults);
    }
    
    return results;
  }

  // 分页查询优化
  static async paginatedQuery<T>(
    model: any,
    options: {
      page: number;
      pageSize: number;
      where?: any;
      orderBy?: any;
      include?: any;
      select?: any;
      cursor?: any;
    }
  ): Promise<{
    data: T[];
    total: number;
    hasMore: boolean;
    nextCursor?: any;
  }> {
    const { page, pageSize, where, orderBy, include, select, cursor } = options;
    
    // 使用游标分页（性能更好）
    if (cursor) {
      const data = await model.findMany({
        take: pageSize + 1, // 多取一个判断是否还有更多
        cursor: { id: cursor },
        skip: 1, // 跳过游标本身
        where,
        orderBy,
        include,
        select,
      });
      
      const hasMore = data.length > pageSize;
      const items = hasMore ? data.slice(0, -1) : data;
      const nextCursor = hasMore ? items[items.length - 1]?.id : null;
      
      return {
        data: items,
        total: -1, // 游标分页不计算总数
        hasMore,
        nextCursor,
      };
    }
    
    // 传统偏移分页
    const skip = (page - 1) * pageSize;
    
    const [data, total] = await Promise.all([
      model.findMany({
        skip,
        take: pageSize,
        where,
        orderBy,
        include,
        select,
      }),
      model.count({ where }),
    ]);
    
    return {
      data,
      total,
      hasMore: skip + pageSize < total,
    };
  }

  // 缓存查询结果
  static async cachedQuery<T>(
    key: string,
    queryFn: () => Promise<T>,
    ttl: number = 5 * 60 * 1000
  ): Promise<T> {
    const cached = cacheManager.get(key);
    if (cached !== null) {
      return cached;
    }
    
    const result = await queryFn();
    cacheManager.set(key, result, { memoryTTL: ttl });
    
    return result;
  }
}

// 优化的帖子查询
export class PostQueries {
  // 获取帖子列表（优化版）
  static async getPosts(options: {
    page?: number;
    pageSize?: number;
    cursor?: number;
    category?: string;
    search?: string;
    authorId?: number;
    tags?: string[];
  }) {
    const {
      page = 1,
      pageSize = 20,
      cursor,
      category,
      search,
      authorId,
      tags,
    } = options;

    // 构建查询条件
    const where: any = {
      published: true,
    };

    if (category) {
      where.tags = {
        has: category,
      };
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (authorId) {
      where.authorId = authorId;
    }

    if (tags && tags.length > 0) {
      where.tags = {
        hasEvery: tags,
      };
    }

    // 优化的查询（只选择必要字段）
    const select = {
      id: true,
      title: true,
      content: true,
      images: true,
      tags: true,
      views: true,
      createdAt: true,
      updatedAt: true,
      author: {
        select: {
          id: true,
          name: true,
          image: true,
        },
      },
      _count: {
        select: {
          comments: true,
          likes: true,
        },
      },
    };

    const cacheKey = `posts:${JSON.stringify({ page, pageSize, cursor, category, search, authorId, tags })}`;
    
    return DatabaseOptimizer.cachedQuery(
      cacheKey,
      () => DatabaseOptimizer.paginatedQuery(prisma.post, {
        page,
        pageSize,
        cursor,
        where,
        orderBy: { createdAt: 'desc' },
        select,
      }),
      2 * 60 * 1000 // 2分钟缓存
    );
  }

  // 获取热门帖子
  static async getHotPosts(limit: number = 10) {
    const cacheKey = `hot_posts:${limit}`;
    
    return DatabaseOptimizer.cachedQuery(
      cacheKey,
      async () => {
        // 基于浏览量和评论数的热门算法
        const posts = await prisma.post.findMany({
          where: {
            published: true,
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
            },
          },
          select: {
            id: true,
            title: true,
            content: true,
            images: true,
            views: true,
            createdAt: true,
            author: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
            _count: {
              select: {
                comments: true,
                likes: true,
              },
            },
          },
          orderBy: [
            { views: 'desc' },
            { createdAt: 'desc' },
          ],
          take: limit,
        });

        return posts;
      },
      10 * 60 * 1000 // 10分钟缓存
    );
  }

  // 获取相关帖子
  static async getRelatedPosts(postId: number, tags: string[], limit: number = 5) {
    const cacheKey = `related_posts:${postId}:${tags.join(',')}:${limit}`;
    
    return DatabaseOptimizer.cachedQuery(
      cacheKey,
      async () => {
        if (tags.length === 0) return [];

        const posts = await prisma.post.findMany({
          where: {
            published: true,
            id: { not: postId },
            tags: {
              hasSome: tags,
            },
          },
          select: {
            id: true,
            title: true,
            images: true,
            views: true,
            createdAt: true,
            author: {
              select: {
                name: true,
                image: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: limit,
        });

        return posts;
      },
      15 * 60 * 1000 // 15分钟缓存
    );
  }
}

// 优化的用户查询
export class UserQueries {
  // 获取用户统计信息
  static async getUserStats(userId: number) {
    const cacheKey = `user_stats:${userId}`;
    
    return DatabaseOptimizer.cachedQuery(
      cacheKey,
      async () => {
        const [postsCount, commentsCount] = await Promise.all([
          prisma.post.count({
            where: { authorId: userId, published: true },
          }),
          prisma.comment.count({
            where: { authorId: userId },
          }),
        ]);

        return {
          postsCount,
          commentsCount,
        };
      },
      5 * 60 * 1000 // 5分钟缓存
    );
  }
}

// 数据库连接池优化
export function optimizePrismaConnection() {
  // 在生产环境中优化连接池
  if (process.env.NODE_ENV === 'production') {
    // 这些配置应该在 DATABASE_URL 中设置
    console.log('Database connection optimized for production');
  }
}
