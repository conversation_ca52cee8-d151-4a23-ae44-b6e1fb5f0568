// 内存缓存管理
class MemoryCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private maxSize: number;

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  set(key: string, data: any, ttl: number = 5 * 60 * 1000): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 浏览器存储缓存
class BrowserCache {
  private prefix: string;

  constructor(prefix: string = 'hxzy_cache_') {
    this.prefix = prefix;
  }

  set(key: string, data: any, ttl: number = 30 * 60 * 1000): void {
    try {
      const item = {
        data,
        timestamp: Date.now(),
        ttl,
      };
      localStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set cache in localStorage:', error);
    }
  }

  get(key: string): any | null {
    try {
      const itemStr = localStorage.getItem(this.prefix + key);
      if (!itemStr) return null;

      const item = JSON.parse(itemStr);
      
      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        this.delete(key);
        return null;
      }
      
      return item.data;
    } catch (error) {
      console.warn('Failed to get cache from localStorage:', error);
      return null;
    }
  }

  delete(key: string): void {
    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.warn('Failed to delete cache from localStorage:', error);
    }
  }

  clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear cache from localStorage:', error);
    }
  }
}

// 统一缓存管理器
class CacheManager {
  private memoryCache: MemoryCache;
  private browserCache: BrowserCache;

  constructor() {
    this.memoryCache = new MemoryCache(200);
    this.browserCache = new BrowserCache();
    
    // 定期清理过期缓存
    setInterval(() => {
      this.memoryCache.cleanup();
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }

  // 设置缓存（同时设置内存和浏览器缓存）
  set(key: string, data: any, options: {
    memoryTTL?: number;
    browserTTL?: number;
    memoryOnly?: boolean;
    browserOnly?: boolean;
  } = {}): void {
    const {
      memoryTTL = 5 * 60 * 1000, // 5分钟
      browserTTL = 30 * 60 * 1000, // 30分钟
      memoryOnly = false,
      browserOnly = false,
    } = options;

    if (!browserOnly) {
      this.memoryCache.set(key, data, memoryTTL);
    }
    
    if (!memoryOnly) {
      this.browserCache.set(key, data, browserTTL);
    }
  }

  // 获取缓存（优先从内存获取）
  get(key: string): any | null {
    // 先从内存缓存获取
    let data = this.memoryCache.get(key);
    if (data !== null) return data;

    // 再从浏览器缓存获取
    data = this.browserCache.get(key);
    if (data !== null) {
      // 重新设置到内存缓存
      this.memoryCache.set(key, data);
      return data;
    }

    return null;
  }

  // 删除缓存
  delete(key: string): void {
    this.memoryCache.delete(key);
    this.browserCache.delete(key);
  }

  // 清空所有缓存
  clear(): void {
    this.memoryCache.clear();
    this.browserCache.clear();
  }
}

// 创建全局缓存实例
export const cacheManager = new CacheManager();

// 缓存装饰器
export function cached(
  key: string | ((args: any[]) => string),
  ttl: number = 5 * 60 * 1000
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = typeof key === 'function' ? key(args) : key;
      
      // 尝试从缓存获取
      const cached = cacheManager.get(cacheKey);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await method.apply(this, args);
      
      // 缓存结果
      cacheManager.set(cacheKey, result, { memoryTTL: ttl });
      
      return result;
    };
  };
}

// SWR 风格的数据获取 Hook
export function useCachedSWR<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    revalidateOnFocus?: boolean;
    revalidateInterval?: number;
    dedupingInterval?: number;
  } = {}
) {
  const {
    revalidateOnFocus = true,
    revalidateInterval = 0,
    dedupingInterval = 2000,
  } = options;

  // 实现 SWR 逻辑...
  // 这里可以集成 SWR 库或自己实现
}
