export const IMAGES = {
  // 首页特色图片
  features: [
    {
      title: '传统医学智慧',
      url: 'https://images.unsplash.com/photo-1584017911766-d451b3d0e843',
      blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRg...',
      width: 1920,
      height: 1280,
      alt: '中医传统诊疗',
    },
    {
      title: '现代科研创新',
      url: 'https://images.unsplash.com/photo-1576671081837-49000212a370',
      blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRg...',
      width: 1920,
      height: 1280,
      alt: '中医药研究',
    },
    {
      title: '健康生活方式',
      url: 'https://images.unsplash.com/photo-1611859266238-4b98091d9d9b',
      blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRg...',
      width: 1920,
      height: 1280,
      alt: '中医养生',
    }
  ],
  
  // 中药材图片
  herbs: [
    {
      title: '人参',
      url: 'https://images.unsplash.com/photo-1543362906-acfc16c67564',
      blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRg...',
      width: 1920,
      height: 1280,
      alt: '人参',
    },
    {
      title: '枸杞',
      url: 'https://images.unsplash.com/photo-1576671081837-49000212a370',
      blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRg...',
      width: 1920,
      height: 1280,
      alt: '枸杞',
    }
  ],
  
  // 头像占位图
  avatars: [
    '/images/avatars/default-1.jpg',
    '/images/avatars/default-2.jpg',
  ],
  
  // 背景图案
  patterns: {
    noise: '/images/patterns/noise.png',
    grid: '/images/patterns/grid.png',
  }
} 