"use client";

import { useState } from 'react';
import OptimizedImage from '@/components/OptimizedImage';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';

export default function TestOptimization() {
  const [items, setItems] = useState<number[]>(Array.from({ length: 20 }, (_, i) => i));
  const [loading, setLoading] = useState(false);

  const { lastElementRef, hasMore, setHasMore } = useInfiniteScroll(
    async () => {
      setLoading(true);
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newItems = Array.from({ length: 10 }, (_, i) => items.length + i);
      setItems(prev => [...prev, ...newItems]);
      
      // 模拟数据加载完毕
      if (items.length > 100) {
        setHasMore(false);
      }
      
      setLoading(false);
    },
    {
      threshold: 100,
      enabled: true,
      delay: 300,
    }
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">性能优化测试页面</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map((item, index) => (
          <div key={item} className="bg-white rounded-lg shadow-md overflow-hidden">
            <OptimizedImage
              src={`https://picsum.photos/400/300?random=${item}`}
              alt={`测试图片 ${item}`}
              width={400}
              height={300}
              className="w-full h-48"
              priority={index < 6} // 前6张图片优先加载
              quality={75}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            <div className="p-4">
              <h3 className="text-lg font-semibold">项目 {item}</h3>
              <p className="text-gray-600">这是第 {item} 个测试项目</p>
            </div>
          </div>
        ))}
      </div>

      {/* 无限滚动加载指示器 */}
      {hasMore && (
        <div
          ref={lastElementRef}
          className="flex justify-center items-center py-8"
        >
          {loading ? (
            <div className="flex items-center gap-2 text-gray-500">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
              <span>加载中...</span>
            </div>
          ) : (
            <div className="text-gray-400">滚动加载更多</div>
          )}
        </div>
      )}

      {!hasMore && (
        <div className="text-center py-8 text-gray-500">
          没有更多内容了
        </div>
      )}
    </div>
  );
}
