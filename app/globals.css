@tailwind base;
@tailwind components;
@tailwind utilities;



@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800 shadow-sm focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 active:bg-secondary-800 shadow-sm focus:ring-secondary-500;
  }

  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-50 hover:text-primary-700 focus:ring-primary-500;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-neutral-100 text-neutral-700 focus:ring-neutral-500;
  }

  .btn-link {
    @apply text-primary-600 hover:text-primary-700 underline-offset-4 hover:underline focus:ring-primary-500;
  }

  .input {
    @apply px-4 py-2 rounded-lg border border-neutral-300 bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-neutral-200 hover:shadow-md transition-shadow duration-200;
  }

  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Twitter风格的按钮 */
  .btn-twitter {
    @apply bg-primary-600 text-white hover:bg-primary-700 rounded-full px-6 py-2 font-semibold transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-twitter-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-50 rounded-full px-6 py-2 font-semibold transition-all duration-200;
  }

  .btn-twitter-small {
    @apply bg-primary-600 text-white hover:bg-primary-700 rounded-full px-4 py-1.5 text-sm font-semibold transition-all duration-200;
  }

  /* Twitter风格的输入框 */
  .input-twitter {
    @apply w-full resize-none border-none outline-none text-xl placeholder-neutral-500 bg-transparent focus:ring-0;
  }

  /* Twitter风格的卡片 */
  .card-twitter {
    @apply bg-white border-b border-neutral-200 hover:bg-neutral-50/50 transition-colors duration-200;
  }


}

/* 自定义样式 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 30;
  background-color: rgba(0, 0, 0, 0.5);
}

.content-container {
  @apply bg-white rounded-xl shadow-sm border border-neutral-200 p-6;
}

.section-bg {
  background-color: rgb(var(--background-secondary));
}

/* 现代化的渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, rgb(var(--primary-rgb) / 0.05) 0%, rgb(var(--secondary-rgb) / 0.05) 100%);
}

.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--primary-rgb)) 0%, rgb(var(--secondary-rgb)) 100%);
}

/* 优化的阴影效果 */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-twitter {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

/* Ant Design Input 搜索框主题色定制 */
.ant-input-search-large .ant-input {
  border-color: #fbd4a5 !important; /* primary-300 */
}

.ant-input-search-large .ant-input:hover {
  border-color: #f7b76d !important; /* primary-400 */
}

.ant-input-search-large .ant-input:focus,
.ant-input-search-large .ant-input-focused {
  border-color: #d97706 !important; /* primary-600 */
  box-shadow: 0 0 0 2px rgba(217, 119, 6, 0.1) !important;
}

.ant-input-search-large .ant-input-group-addon {
  border-color: #d97706 !important; /* primary-600 */
  background-color: #d97706 !important; /* primary-600 */
}

.ant-input-search-large .ant-input-group-addon .ant-btn {
  background-color: #d97706 !important; /* primary-600 */
  border-color: #d97706 !important; /* primary-600 */
  color: white !important;
}

.ant-input-search-large .ant-input-group-addon .ant-btn:hover {
  background-color: #b45309 !important; /* primary-700 */
  border-color: #b45309 !important; /* primary-700 */
}
