version: '3.8'

services:
  # PostgreSQL 数据库 (开发环境)
  postgres-dev:
    image: postgres:15-alpine
    container_name: hxzy-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: hxzy_dev
      POSTGRES_USER: hxzy_dev_user
      POSTGRES_PASSWORD: hxzy_dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - hxzy-dev-network

  # Redis 缓存 (开发环境)
  redis-dev:
    image: redis:7-alpine
    container_name: hxzy-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"
    networks:
      - hxzy-dev-network

  # 开发环境应用 (热重载)
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: hxzy-app-dev
    restart: unless-stopped
    environment:
      DATABASE_URL: "**************************************************************/hxzy_dev"
      NEXTAUTH_URL: "http://localhost:3001"
      NEXTAUTH_SECRET: "dev-secret-key"
      STORAGE_TYPE: "local"
      LOCAL_UPLOAD_DIR: "public/uploads"
      LOCAL_BASE_URL: "http://localhost:3001"
      NODE_ENV: "development"
      REDIS_URL: "redis://redis-dev:6379"
    ports:
      - "3001:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
      - uploads_dev_data:/app/public/uploads
    networks:
      - hxzy-dev-network
    depends_on:
      - postgres-dev
      - redis-dev
    command: npm run dev

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  uploads_dev_data:
    driver: local

networks:
  hxzy-dev-network:
    driver: bridge
