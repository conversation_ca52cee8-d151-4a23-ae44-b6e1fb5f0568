"use client";

import React from 'react';
import Link from 'next/link';
import { Tag } from 'antd';
import { EyeOutlined, MessageOutlined } from '@ant-design/icons';
import OptimizedImage from './OptimizedImage';
import LikeButton from './LikeButton';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

interface Post {
  id: string;
  title?: string;
  content: string;
  images?: string[];
  tags: string[];
  views: number;
  createdAt: string;
  author: {
    name: string | null;
    image: string | null;
  };
  _count: {
    comments: number;
    likes: number;
  };
}

interface VirtualPostListProps {
  posts: Post[];
  totalHeight: number;
  offsetY: number;
  containerHeight: number;
  onScroll: (e: React.UIEvent<HTMLDivElement>) => void;
  itemHeight: number;
  startIndex: number;
}

export default function VirtualPostList({
  posts,
  totalHeight,
  offsetY,
  containerHeight,
  onScroll,
  itemHeight,
  startIndex,
}: VirtualPostListProps) {
  return (
    <div
      className="virtual-scroll-container overflow-auto"
      style={{ height: containerHeight }}
      onScroll={onScroll}
    >
      {/* 总高度占位符 */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* 可见项目容器 */}
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {posts.map((post, index) => {
            const actualIndex = startIndex + index;
            return (
              <div
                key={post.id}
                className="virtual-post-item"
                style={{ height: itemHeight }}
              >
                <PostItem post={post} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

// 单个帖子项组件
function PostItem({ post }: { post: Post }) {
  return (
    <div className="group bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 hover:border-gray-200 p-4 mb-3">
      <div className="flex gap-4">
        {/* 左侧内容 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start gap-3 mb-3">
            {/* 用户头像 */}
            <div className="relative">
              {post.author.image ? (
                <OptimizedImage
                  src={post.author.image}
                  alt={post.author.name || "用户头像"}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-full object-cover shadow-lg outline outline-3 outline-primary-600"
                  priority={false}
                  quality={80}
                />
              ) : (
                <div className="w-10 h-10 rounded-full flex outline outline-3 outline-primary-600 items-center justify-center shadow-lg">
                  <span className="text-sm font-medium text-primary-600">
                    {post.author.name?.charAt(0) || "U"}
                  </span>
                </div>
              )}
            </div>

            {/* 用户信息和时间 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-gray-900 truncate">
                  {post.author.name}
                </span>
                <span className="text-gray-400 text-sm">·</span>
                <span className="text-gray-500 text-sm whitespace-nowrap">
                  {dayjs(post.createdAt).fromNow()}
                </span>
              </div>
            </div>
          </div>

          {/* 帖子内容 */}
          <Link href={`/community/${post.id}`} className="block group/content">
            {post.title && (
              <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover/content:text-primary-600 transition-colors">
                {post.title}
              </h3>
            )}
            <p className="text-gray-600 text-sm line-clamp-3 mb-3 leading-relaxed">
              {post.content}
            </p>
          </Link>

          {/* 标签 */}
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {post.tags.slice(0, 3).map((tag) => (
                <Tag
                  key={tag}
                  className="text-xs px-2 py-1 bg-primary-50 text-primary-700 border-primary-200 rounded-full"
                >
                  {tag}
                </Tag>
              ))}
              {post.tags.length > 3 && (
                <span className="text-xs text-gray-400">+{post.tags.length - 3}</span>
              )}
            </div>
          )}

          {/* 互动数据 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1 text-primary-600 hover:text-primary-700 transition-colors group/view">
                <EyeOutlined className="w-4 h-4 group-hover/view:scale-110 transition-transform" />
                <span className="text-xs font-medium">{post.views}</span>
              </div>

              <div className="flex items-center gap-1 text-primary-600 hover:text-primary-700 transition-colors">
                <MessageOutlined className="w-4 h-4" />
                <span className="text-xs font-medium">{post._count.comments}</span>
              </div>
            </div>

            <LikeButton
              postId={post.id}
              initialLikes={post._count.likes}
              size="small"
            />
          </div>
        </div>

        {/* 右侧图片区域 */}
        {post.images && post.images.length > 0 && (
          <Link
            href={`/community/${post.id}`}
            className="block flex-shrink-0 w-20 h-16"
          >
            <div className="relative overflow-hidden rounded-lg bg-gray-100 w-full h-full shadow-sm ring-1 ring-gray-200/50">
              <OptimizedImage
                src={post.images[0]}
                alt="帖子图片"
                width={80}
                height={64}
                className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                priority={false}
                quality={75}
                sizes="80px"
              />
              {post.images.length > 1 && (
                <div className="absolute top-1 right-1 bg-black/80 text-white text-xs px-1.5 py-0.5 rounded-md font-medium">
                  +{post.images.length - 1}
                </div>
              )}
            </div>
          </Link>
        )}
      </div>
    </div>
  );
}
