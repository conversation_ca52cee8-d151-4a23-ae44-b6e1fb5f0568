#!/bin/bash

# 华夏中医网站部署脚本
set -e

echo "🚀 开始部署华夏中医网站..."

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.docker .env
    echo "⚠️  请编辑 .env 文件并填入正确的配置值"
    echo "⚠️  特别是第三方登录的 Client ID 和 Secret"
    read -p "按回车键继续..."
fi

# 选择部署模式
echo "请选择部署模式："
echo "1) 生产环境部署"
echo "2) 开发环境部署"
read -p "请输入选择 (1-2): " choice

case $choice in
    1)
        echo "🏭 启动生产环境..."
        
        # 构建并启动服务
        docker-compose down
        docker-compose build --no-cache
        docker-compose up -d
        
        # 等待数据库启动
        echo "⏳ 等待数据库启动..."
        sleep 10
        
        # 运行数据库迁移
        echo "🗄️ 运行数据库迁移..."
        docker-compose exec app npx prisma migrate deploy
        
        echo "✅ 生产环境部署完成！"
        echo "🌐 访问地址: http://localhost:3000"
        ;;
    2)
        echo "🛠️ 启动开发环境..."
        
        # 启动开发环境
        docker-compose -f docker-compose.dev.yml down
        docker-compose -f docker-compose.dev.yml build --no-cache
        docker-compose -f docker-compose.dev.yml up -d
        
        # 等待数据库启动
        echo "⏳ 等待数据库启动..."
        sleep 10
        
        # 运行数据库迁移
        echo "🗄️ 运行数据库迁移..."
        docker-compose -f docker-compose.dev.yml exec app-dev npx prisma migrate dev
        
        echo "✅ 开发环境部署完成！"
        echo "🌐 访问地址: http://localhost:3001"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 显示服务状态
echo ""
echo "📊 服务状态："
if [ $choice -eq 1 ]; then
    docker-compose ps
else
    docker-compose -f docker-compose.dev.yml ps
fi

echo ""
echo "📋 有用的命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  进入容器: docker-compose exec app bash"

echo ""
echo "🎉 部署完成！"
