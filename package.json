{"name": "huaxia-tcm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "start": "next start", "build": "next build", "start:prod": "NODE_ENV=production next start", "dev:prod": "NODE_ENV=production next dev", "build:prod": "prisma generate && prisma migrate deploy && next build", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.842.0", "@prisma/client": "^5.10.2", "@types/lodash": "^4.17.20", "@types/xml2js": "^0.4.14", "antd": "^5.24.6", "autoprefixer": "^10.4.21", "dayjs": "^1.11.10", "framer-motion": "^11.18.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lunar-javascript": "^1.6.13", "next": "15.1.4", "next-auth": "^4.24.6", "postcss": "^8.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "resend": "^4.5.1", "swr": "^2.2.5", "xml2js": "^0.6.2"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "eslint": "^8.57.0", "eslint-config-next": "15.1.4", "prisma": "^5.10.2", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}