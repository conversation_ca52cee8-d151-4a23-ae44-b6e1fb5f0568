version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: hxzy-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: hxzy
      POSTGRES_USER: hxzy_user
      POSTGRES_PASSWORD: hxzy_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - hxzy-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hxzy_user -d hxzy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: hxzy-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - hxzy-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js 应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hxzy-app
    restart: unless-stopped
    environment:
      # 数据库配置
      DATABASE_URL: "**************************************************/hxzy"
      
      # NextAuth 配置
      NEXTAUTH_URL: "http://localhost:3000"
      NEXTAUTH_SECRET: "your-nextauth-secret-key-change-this-in-production"
      
      # 第三方登录配置 (需要替换为实际值)
      GITHUB_CLIENT_ID: "${GITHUB_CLIENT_ID:-}"
      GITHUB_CLIENT_SECRET: "${GITHUB_CLIENT_SECRET:-}"
      GOOGLE_CLIENT_ID: "${GOOGLE_CLIENT_ID:-}"
      GOOGLE_CLIENT_SECRET: "${GOOGLE_CLIENT_SECRET:-}"
      
      # 微信登录配置
      WECHAT_APP_ID: "${WECHAT_APP_ID:-}"
      WECHAT_APP_SECRET: "${WECHAT_APP_SECRET:-}"
      WECHAT_REDIRECT_URL: "http://localhost:3000/api/wechat/callback"
      
      # 存储配置
      STORAGE_TYPE: "local"
      LOCAL_UPLOAD_DIR: "public/uploads"
      LOCAL_BASE_URL: "http://localhost:3000"
      
      # R2 配置 (可选)
      R2_ACCOUNT_ID: "${R2_ACCOUNT_ID:-}"
      R2_ACCESS_KEY_ID: "${R2_ACCESS_KEY_ID:-}"
      R2_SECRET_ACCESS_KEY: "${R2_SECRET_ACCESS_KEY:-}"
      R2_BUCKET_NAME: "${R2_BUCKET_NAME:-hxzy}"
      R2_ENDPOINT: "${R2_ENDPOINT:-}"
      R2_PUBLIC_URL: "${R2_PUBLIC_URL:-}"
      
      # 文件上传限制
      MAX_FILE_SIZE: "5242880"
      ALLOWED_FILE_TYPES: "image/jpeg,image/png,image/webp,image/gif"
      MAX_FILES_PER_UPLOAD: "9"
      
      # Redis 配置
      REDIS_URL: "redis://:redis_password@redis:6379"
      
      # 生产环境配置
      NODE_ENV: "production"
    ports:
      - "3000:3000"
    volumes:
      - uploads_data:/app/public/uploads
    networks:
      - hxzy-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: hxzy-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
    networks:
      - hxzy-network
    depends_on:
      - app

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local

networks:
  hxzy-network:
    driver: bridge
